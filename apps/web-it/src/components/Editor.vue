<script setup>
import { ref } from 'vue';

import Editor from '@tinymce/tinymce-vue';

// 导入 TinyMCE 核心
import 'tinymce/tinymce';
import 'tinymce/themes/silver';
import 'tinymce/icons/default';
import 'tinymce/plugins/code';
import 'tinymce/plugins/lists';
import 'tinymce/plugins/link';
import 'tinymce/plugins/image';
import 'tinymce/plugins/table';
import 'tinymce/plugins/wordcount';
import 'tinymce/plugins/help';

const editorContent = ref('');

// TinyMCE 初始化配置
const editorConfig = {
  language: 'zh-Hans',
  base_url: '/tinymce',
  skin_url: '/tinymce/skins/ui/oxide',
  content_css: '/tinymce/skins/content/default/content.css',
  menubar: true,
  plugins: 'lists link image table code help wordcount',
  toolbar:
    'undo redo | formatselect | bold italic | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | link image table code | help',
  height: 500,
};
</script>

<template>
  <div>
    <Editor v-model="editorContent" :init="editorConfig" />
  </div>
</template>

<style>
</style>
