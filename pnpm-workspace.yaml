packages:
  - internal/*
  - internal/lint-configs/*
  - packages/*
  - packages/@core/base/*
  - packages/@core/ui-kit/*
  - packages/@core/forward/*
  - packages/@core/*
  - packages/effects/*
  - packages/business/*
  - apps/*
  - scripts/*
  - docs
  - playground
catalog:
  '@ast-grep/napi': ^0.37.0
  '@ant-design/colors': ^7.2.0
  '@ant-design/icons-vue': ^7.0.1
  '@changesets/changelog-github': ^0.5.1
  '@changesets/cli': ^2.29.2
  '@changesets/git': ^3.0.4
  '@clack/prompts': ^0.10.1
  '@commitlint/cli': ^19.8.0
  '@commitlint/config-conventional': ^19.8.0
  '@ctrl/tinycolor': ^4.1.0
  '@eslint/js': ^9.26.0
  '@faker-js/faker': ^9.7.0
  '@form-create/ant-design-vue': ^3.2.22
  '@form-create/antd-designer': ^3.2.11
  '@iconify/json': ^2.2.334
  '@iconify/tailwind': ^1.2.0
  '@iconify/vue': ^5.0.0
  '@intlify/core-base': ^11.1.3
  '@intlify/unplugin-vue-i18n': ^6.0.8
  '@jspm/generator': ^2.5.1
  '@manypkg/get-packages': ^3.0.0
  '@nolebase/vitepress-plugin-git-changelog': ^2.17.0
  '@playwright/test': ^1.52.0
  '@pnpm/workspace.read-manifest': ^1000.1.4
  '@purge-icons/generated': ^0.10.0
  '@stylistic/stylelint-plugin': ^3.1.2
  '@tailwindcss/nesting': 0.0.0-insiders.565cd3e
  '@tailwindcss/typography': ^0.5.16
  '@tailwindcss/container-queries': ^0.1.1
  '@tanstack/vue-query': ^5.75.1
  '@tanstack/vue-store': ^0.7.0
  '@tinymce/tinymce-vue': ^6.3.0
  '@types/archiver': ^6.0.3
  '@types/eslint': ^9.6.1
  '@types/html-minifier-terser': ^7.0.2
  '@types/json-bigint': ^1.0.4
  '@types/jsonwebtoken': ^9.0.9
  '@types/lodash-es': ^4.17.12
  '@types/lodash.clonedeep': ^4.5.9
  '@types/lodash.get': ^4.4.9
  '@types/lodash.isequal': ^4.5.8
  '@types/lodash.defaultsdeep': ^4.6.9
  '@types/lodash.set': ^4.3.9
  '@types/node': ^22.15.3
  '@types/nprogress': ^0.2.3
  '@types/postcss-import': ^14.0.3
  '@types/qrcode': ^1.5.5
  '@types/qs': ^6.9.18
  '@types/sortablejs': ^1.15.8
  '@types/spark-md5': ^3.0.5
  '@types/sm-crypto': ^0.3.4
  '@types/tldjs': ^2.3.2
  '@typescript-eslint/eslint-plugin': ^8.31.1
  '@typescript-eslint/parser': ^8.31.1
  '@vee-validate/zod': ^4.15.0
  '@vite-pwa/vitepress': ^1.0.0
  '@vitejs/plugin-vue': ^5.2.3
  '@vitejs/plugin-vue-jsx': ^4.1.2
  '@vue/reactivity': ^3.5.13
  '@vue/shared': ^3.5.13
  '@vue/test-utils': ^2.4.6
  '@vue/runtime-core': ^3.5.13
  '@vueuse/core': ^13.1.0
  '@vueuse/integrations': ^13.1.0
  '@vueuse/motion': ^3.0.3
  ant-design-vue: ^4.2.6
  archiver: ^7.0.1
  autoprefixer: ^10.4.21
  axios: ^1.9.0
  axios-mock-adapter: ^2.1.0
  bignumber.js: ^9.1.2
  cac: ^6.7.14
  chalk: ^5.4.1
  cheerio: ^1.0.0
  circular-dependency-scanner: ^2.3.0
  class-variance-authority: ^0.7.1
  clsx: ^2.1.1
  commitlint-plugin-function-rules: ^4.0.1
  consola: ^3.4.2
  cross-env: ^7.0.3
  crypto-js: ^4.2.0
  cspell: ^8.19.3
  cssnano: ^7.0.6
  cz-git: ^1.11.1
  czg: ^1.11.1
  dayjs: ^1.11.13
  defu: ^6.1.4
  depcheck: ^1.4.7
  dotenv: ^16.5.0
  echarts: ^5.6.0
  element-plus: ^2.9.9
  eslint: ^9.26.0
  eslint-config-turbo: ^2.5.2
  eslint-plugin-command: ^3.2.0
  eslint-plugin-eslint-comments: ^3.2.0
  eslint-plugin-import-x: ^4.11.0
  eslint-plugin-jsdoc: ^50.6.11
  eslint-plugin-jsonc: ^2.20.0
  eslint-plugin-n: ^17.17.0
  eslint-plugin-no-only-tests: ^3.3.0
  eslint-plugin-perfectionist: ^4.12.3
  eslint-plugin-prettier: ^5.2.6
  eslint-plugin-regexp: ^2.7.0
  eslint-plugin-unicorn: ^59.0.0
  eslint-plugin-unused-imports: ^4.1.4
  eslint-plugin-vitest: ^0.5.4
  eslint-plugin-vue: ^10.1.0
  lodash-es: ^4.17.21
  execa: ^9.5.2
  find-up: ^7.0.0
  get-port: ^7.1.0
  globals: ^16.0.0
  h3: ^1.15.3
  happy-dom: ^17.4.6
  html-minifier-terser: ^7.2.0
  is-ci: ^4.1.0
  json-bigint: ^1.0.0
  jsonc-eslint-parser: ^2.4.0
  jsonwebtoken: ^9.0.2
  lefthook: ^1.11.12
  less: ^4.3.0
  lodash.clonedeep: ^4.5.0
  lodash.defaultsdeep: ^4.6.1
  lodash.get: ^4.4.2
  lodash.isequal: ^4.5.0
  lodash.set: ^4.3.2
  lucide-vue-next: ^0.507.0
  lunar-typescript: ^1.7.8
  medium-zoom: ^1.1.0
  naive-ui: ^2.41.0
  nitropack: ^2.11.11
  nprogress: ^0.2.0
  nzh: ^1.0.14
  ora: ^8.2.0
  pinia: ^3.0.2
  pinia-plugin-persistedstate: ^4.2.0
  pkg-types: ^2.1.0
  playwright: ^1.52.0
  postcss: ^8.5.3
  postcss-antd-fixes: ^0.2.0
  postcss-html: ^1.8.0
  postcss-import: ^16.1.0
  postcss-preset-env: ^10.1.6
  postcss-scss: ^4.0.9
  prettier: ^3.5.3
  prettier-plugin-tailwindcss: ^0.6.11
  publint: ^0.3.12
  qrcode: ^1.5.4
  qs: ^6.14.0
  radix-vue: ^1.9.17
  resolve.exports: ^2.0.3
  rimraf: ^6.0.1
  rollup: ^4.40.1
  rollup-plugin-visualizer: ^5.14.0
  sass: ^1.87.0
  secure-ls: ^2.0.0
  sortablejs: ^1.15.6
  stylelint: ^16.19.1
  stylelint-config-recess-order: ^6.0.0
  stylelint-config-recommended: ^16.0.0
  stylelint-config-recommended-scss: ^14.1.0
  stylelint-config-recommended-vue: ^1.6.0
  stylelint-config-standard: ^38.0.0
  stylelint-order: ^7.0.0
  stylelint-prettier: ^5.0.3
  stylelint-scss: ^6.11.1
  tailwind-merge: ^2.6.0
  tailwindcss: ^3.4.17
  tailwindcss-animate: ^1.0.7
  tinymce: ^8.0.2
  theme-colors: ^0.1.0
  tippy.js: ^6.3.7
  turbo: ^2.5.2
  typescript: ^5.8.3
  unbuild: ^3.5.0
  unplugin-element-plus: ^0.10.0
  universal-cookie: ^8.0.1
  vee-validate: ^4.15.0
  vite: ^6.3.4
  vite-plugin-compression: ^0.5.1
  vite-plugin-dts: ^4.5.3
  vite-plugin-html: ^3.2.2
  vite-plugin-lazy-import: ^1.0.7
  vite-plugin-pwa: ^1.0.0
  vite-plugin-vue-devtools: ^7.7.6
  vite-plugin-static-copy: ^3.1.2
  vitepress: ^1.6.3
  vitepress-plugin-group-icons: ^1.5.2
  vitest: ^3.1.2
  vue: ^3.5.13
  vue-currency-input: ^3.2.1
  vue-eslint-parser: ^10.1.3
  vue-i18n: ^11.1.3
  vue-json-viewer: ^3.0.4
  vue-router: ^4.5.1
  vue-simple-uploader: ^1.0.3
  vue-tippy: ^6.7.0
  vue-tsc: 2.2.10
  vue-types: ^5.1.3
  vue3-signature: ^0.2.4
  vxe-pc-ui: ^4.5.35
  vxe-table: 4.13.16
  watermark-js-plus: ^1.6.0
  resize-observer-polyfill: ^1.5.1
  spark-md5: ^3.0.2
  sm-crypto: ^0.3.13
  tldjs: ^2.3.2
  zod: ^3.24.3
  zod-defaults: ^0.1.3
